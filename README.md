## Miao-Vuefinder File Manager

**This is a special version, remove the edit function, and add Chinese;


![GitHub](https://img.shields.io/github/license/WitMiao/vuefinder)
![npm](https://img.shields.io/npm/v/miao-vuefinder)

[//]: # (![npm]&#40;https://img.shields.io/npm/dw/miao-vuefinder&#41;)

![ezgif-1-b902690b76](https://user-images.githubusercontent.com/712404/193141338-8d5f726f-da1a-4825-b652-28e4007493db.gif)

### About

Vuefinder is a file manager component for Vue.js version 3

### Origin Demo

[Live Demo](https://vuefinder.ozdemir.be/) [ [Source](https://github.com/n1crack/vuefinder.ozdemir.be) ]

### Installation

```bash
npm i miao-vuefinder
```

JS entry point (it can be index.js or main.js)

```js
import { createApp } from 'vue'
import App from './App.vue'

import 'miao-vuefinder/dist/style.css'
import VueFinder from 'miao-vuefinder/dist/vuefinder'

const app = createApp(App)

app.use(VueFinder)

app.mount('#app')
 
```

Html

```html
...
<div>
    <vue-finder id='my_vuefinder' url="http://vuefinder-php.test"></vue-finder>
</div>
...
```

### Props

| Prop          |  Value  | Default | Description                            |
|---------------|:-------:|---------|:---------------------------------------|
| id            | string  | _null_  | required                               |
| url           | string  | _null_  | required - dir url                     |
| api           | string  | _null_  | required - backend api url             |
| token         | string  | _null_  | required - backend api x-Token         |
| locale        | string  | en      | optional - default language code       |
| dark          | boolean | false   | optional - makes theme dark as default |
| max-file-size | string  | 10mb    | optional - client side max file upload |
| post-data     | object  | {}      | optional - custom post data            |
| full-screen   | boolean | false   | optional - makes full-screen as default|

### Features

- Multi adapter/storage (see <https://github.com/thephpleague/flysystem>)
- File and folder operations
  - Create a new file
  - Create a new folder
  - Rename
  - Delete
  - Archive (zip)
  - Unarchive (unzip)
  - Text editing
  - Image Crop Tool
  - Upload / Download files
  - Search (deep based on current folder)
- Nice UI
  - Context Menu
  - Breadcrumb links
  - Toolbar
  - File explorer
  - Status bar
  - Image thumbnails
  - Toast notifications
- Appearance
  - Multi language
  - Full Screen
  - View Modes: list, grid
  - Dark Mode
- Accessibility
  - Drag & drop support
  - Move items (to a folder or up one folder) with drag and drop
  - Mouse selection

### Backend

- PHP: [VueFinder Php Library](https://github.com/n1crack/vuefinder-php)

### Roadmap

- [ ] code refactoring (cleanup the duplications, make reusable components)
- [ ] restyle the modals
- [ ] add more languages (only en/tr/ru at the moment. PRs are welcomed.)
- [ ] copy/move to a folder (modal, treeview)
- [ ] transfer items between filesystem adapters
- [ ] show/hide components (toolbar/statusbar etc.)
- [ ] emit select event, with @select get selected files for online editors like tinymce/ckeditor
- [ ] drag&drop on folders at address bar
- [ ] update DragSelect plugin for using its dropzone support

### Dependencies

- [Vue3](https://vuejs.org/)
- [Cropperjs](https://github.com/fengyuanchen/cropperjs)  : JavaScript image cropper
- [DragSelect](https://github.com/ThibaultJanBeyer/DragSelect/) : Selection utility
- [Plupload](https://github.com/moxiecode/plupload) : Upload library
- [vanilla-lazyload](https://github.com/verlok/vanilla-lazyload) : lazy loading for thumbnails

### License

Copyright (c) 2023 MiaoDa, released under [the MIT license](LICENSE)
