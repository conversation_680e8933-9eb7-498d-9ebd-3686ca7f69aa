<template>
  <div class="my-container">
    <vue-finder
      id="vue-finder"
      :url="testUrl"
      locale="zh-cn"
      maxHeight="100vh"
      class="finder"
      :api="api"
      :token="token"
    ></vue-finder>
  </div>
</template>

<script lang="ts" setup>
const testUrl = 'uploads/feedback/90ea23d24921911f436254335a301829_05071149/';
const api = '/api/v1/crash/path';
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZTA2YWVhYTMtMDM4Mi00ZDg3LWI5YjQtMGQwZWRlZDkyODJkIiwiSUQiOjQ5MjksIlVzZXJuYW1lIjoiaWhfbGl1Y2hhb3lpIiwiTmlja05hbWUiOiLliJjotoXoiboiLCJBdXRob3JpdHlJZCI6IjEwMjQiLCJCaXpJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MTI1MjcwNSwibmJmIjoxNzUwMDcxNjA5fQ.TMx1od6h7crP15NuyTmWs7LmkIUmzUHZNXhl2Ppl8kc'
</script>

<style>
.my-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}
.finder {
  width: 50%;
}
</style>
