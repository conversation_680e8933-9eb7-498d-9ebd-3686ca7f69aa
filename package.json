{"name": "miao-vuefinder", "version": "0.2.10", "description": "miao-Vuefinder is a file manager component for vuejs.", "type": "module", "files": ["dist"], "main": "./dist/miao-vuefinder.cjs", "module": "./dist/miao-vuefinder.js", "scripts": {"dev": "vite ./example", "build": "vite build", "preview": "vite preview", "pb": "npm publish", "pv": "npm version patch -m \"chore: 版本更新至%s\""}, "homepage": "https://github.com/WitMiao/vuefinder#readme", "keywords": ["vue", "file manager", "file plugin", "file explorer", "finder"], "repository": {"type": "git", "url": "git+https://github.com/WitMiao/vuefinder.git"}, "author": {"name": "MiaoDa", "mail": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@types/node": "^20.1.1", "@vitejs/plugin-vue": "^4.2.1", "autoprefixer": "^10.4.14", "microtip": "^0.2.2", "mitt": "^3.0.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "vite": "^4.3.5"}, "dependencies": {"cropperjs": "^1.5.13", "dragselect": "^2.7.4", "monaco-editor": "^0.52.2", "plupload": "^2.3.9", "vanilla-lazyload": "^17.8.3", "vue": "^3.2.47"}}