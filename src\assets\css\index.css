@tailwind components;

@layer components {
  .vuefinder {
    position: relative;
  }

  .vuefinder * {
    touch-action: manipulation;
  }

  .vf-explorer-selected {
    @apply bg-neutral-100 border border-neutral-300 dark:bg-slate-700 dark:border-gray-900 dark:border-slate-800 !important;
  }

  .vf-explorer-selector {
    @apply border border-slate-500 bg-slate-300 opacity-50 !important;
  }

  .vuefinder.dark {
    color-scheme: dark;
  }

  /* width */
  .vf-selector-area::-webkit-scrollbar {
    width: 12px;
  }

  /* Track */
  .vf-selector-area::-webkit-scrollbar-track-piece {
    @apply bg-gray-100 dark:bg-slate-900/50;
  }

  /* Handle */
  .vf-selector-area::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-slate-700;
  }

  /* Handle on hover */
  .vf-selector-area::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-slate-600;
  }

  .vf-selector-area::-webkit-scrollbar-corner {
    @apply bg-transparent;
  }
}

@tailwind utilities;
